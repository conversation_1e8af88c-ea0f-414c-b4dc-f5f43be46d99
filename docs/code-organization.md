# Code Organization Structure - Best Practices Research

## Key Findings từ GitHub Research

### 1. **Next.js Official Patterns**

- **Private Folders**: Sử dụng `_folder` cho implementation details
- **src/ Organization**: Tách application code khỏi config files
- **Component Hierarchy**: Layout > Template > Error > Loading > Page
- **Route Groups**: `(folder)` để tổ chức routes without affecting URL
- **Constants**: Separate constants files cho configuration

### 2. **shadcn/ui Organization Patterns**

- **Registry System**: Tách components, examples, blocks, internal
- **Type-based Organization**: `registry:ui`, `registry:component`, `registry:example`
- **Constants Separation**: Riêng file cho BUILTIN_MODULES, framework configs
- **Utils Separation**: Dedicated utilities với clear responsibilities
- **Config Schema**: External schemas cho validation

## Recommended Structure for Work Finder

```
src/
├── components/
│   ├── layout/
│   │   ├── header/
│   │   │   ├── index.tsx              # Main export
│   │   │   ├── header.tsx             # Header component
│   │   │   ├── navigation.tsx         # Navigation logic
│   │   │   ├── mobile-nav.tsx         # Mobile navigation
│   │   │   ├── user-menu.tsx          # User dropdown
│   │   │   └── logo.tsx               # Logo component
│   │   ├── footer/
│   │   │   ├── index.tsx
│   │   │   ├── footer.tsx
│   │   │   ├── footer-links.tsx
│   │   │   └── social-links.tsx
│   │   └── index.tsx                  # Layout exports
│   ├── ui/                            # shadcn/ui components
│   └── features/                      # Feature-specific components
├── constants/
│   ├── navigation.ts                  # Navigation configuration
│   ├── company.ts                     # Company branding
│   ├── routes.ts                      # Route definitions
│   └── index.ts                       # Constants exports
├── types/
│   ├── navigation.ts                  # Navigation types
│   ├── layout.ts                      # Layout types
│   ├── common.ts                      # Common types
│   └── index.ts                       # Types exports
├── lib/
│   ├── utils.ts                       # Utility functions
│   ├── cn.ts                          # ClassNames utility
│   └── constants.ts                   # Library constants
└── config/
    ├── site.ts                        # Site configuration
    └── navigation.ts                  # Navigation configuration
```

## Implementation Examples

### 1. **Navigation Constants**

```typescript
// src/constants/navigation.ts
import { type NavigationItem } from "@/types/navigation";

export const NAVIGATION_ITEMS: NavigationItem[] = [
  {
    id: "find-jobs",
    label: "Find Jobs",
    href: "/jobs",
    icon: "Briefcase",
    children: [
      { label: "All Jobs", href: "/jobs", description: "Browse all positions" },
      {
        label: "Remote Jobs",
        href: "/jobs/remote",
        description: "Work from anywhere",
      },
    ],
  },
  // ...
];

export const COMPANY_INFO = {
  name: "WorkFinder",
  shortName: "WF",
  logo: "/logo.svg",
  description: "Find your dream job",
} as const;
```

### 2. **Navigation Types**

```typescript
// src/types/navigation.ts
export interface NavigationItem {
  id: string;
  label: string;
  href: string;
  icon?: string;
  children?: NavigationChild[];
}

export interface NavigationChild {
  label: string;
  href: string;
  description?: string;
}

export interface NavigationProps {
  items: NavigationItem[];
  isScrolled?: boolean;
  isMobile?: boolean;
}
```

### 3. **Component Structure**

```typescript
// src/components/layout/header/index.ts
export { default as Header } from "./header";
export { default as Navigation } from "./navigation";
export { default as Logo } from "./logo";
export * from "./types";

// src/components/layout/header/header.tsx
import { Navigation } from "./navigation";
import { Logo } from "./logo";
import { UserMenu } from "./user-menu";
import { NAVIGATION_ITEMS } from "@/constants/navigation";

export default function Header() {
  return (
    <header>
      <Logo />
      <Navigation items={NAVIGATION_ITEMS} />
      <UserMenu />
    </header>
  );
}
```

### 4. **Logo Component**

```typescript
// src/components/layout/header/logo.tsx
import Link from "next/link";
import { COMPANY_INFO } from "@/constants/navigation";

export default function Logo() {
  return (
    <Link href="/" className="flex items-center space-x-2">
      <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
        <span className="text-white font-bold text-sm">
          {COMPANY_INFO.shortName}
        </span>
      </div>
      <span className="text-xl font-bold">{COMPANY_INFO.name}</span>
    </Link>
  );
}
```

## Benefits of This Structure

### ✅ **Separation of Concerns**

- Components chỉ focus vào rendering
- Constants chứa configuration data
- Types provide type safety
- Utils chứa reusable logic

### ✅ **Maintainability**

- Easy to update navigation items
- Clear file responsibilities
- Consistent naming patterns
- Scalable structure

### ✅ **Developer Experience**

- Better IDE support với types
- Easy to find related files
- Clear import paths
- Reduced coupling

### ✅ **Team Collaboration**

- Clear ownership boundaries
- Consistent patterns
- Easy to understand structure
- Reduced merge conflicts

## Migration Steps

1. **Create new structure folders**
2. **Extract constants from components**
3. **Define TypeScript interfaces**
4. **Refactor components to use constants**
5. **Update imports across project**
6. **Test all functionality**

## Best Practices Applied

1. **Configuration-driven Components**: Components nhận config thay vì hardcode
2. **Type Safety**: Proper TypeScript interfaces
3. **Single Responsibility**: Mỗi file có 1 responsibility rõ ràng
4. **Consistent Naming**: Theo Next.js và React conventions
5. **Scalable Structure**: Dễ dàng thêm features mới

This structure follows patterns from both Next.js and shadcn/ui for optimal organization and maintainability.
