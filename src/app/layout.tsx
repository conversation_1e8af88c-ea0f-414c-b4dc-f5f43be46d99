import type { <PERSON><PERSON><PERSON> } from "next";
import { <PERSON><PERSON> } from "next/font/google";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "@/components/layout";
import "./globals.css";

const jost = Jost({
  variable: "--font-jost",
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "Work Finder - Find Your Dream Job",
  description:
    "Discover thousands of job opportunities and connect with top employers. Your next career move starts here.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${jost.variable} antialiased font-jost`}>
        <div className="min-h-screen flex flex-col">
          <Header />
          <main className="flex-1 relative pt-16 lg:pt-0">{children}</main>
          <Footer />
        </div>
      </body>
    </html>
  );
}
