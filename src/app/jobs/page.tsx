"use client";

import { JobCard } from "@/components/features/jobs/job-card";

// Mock data giống TopCV
const mockJobs = [
  {
    id: "1",
    title: "Leader <PERSON><PERSON>n Thương Mại Điện Tử - <PERSON><PERSON><PERSON> (Thu Nhập 20-25 Triệu)",
    description:
      "Tì<PERSON> kiếm Leader c<PERSON> kinh nghiệm trong lĩnh vực thương mại điện tử, đặc biệt là kênh TikTok",
    summary:
      "Vị trí Leader quản lý sàn thương mại điện tử với thu nhập hấp dẫn từ 20-25 triệu",
    requirements: [
      "Kinh nghiệm 3+ năm trong thương mại điện tử",
      "Hiểu biết sâu về TikTok Shop",
      "Kỹ năng quản lý team",
    ],
    benefits: ["Thu nhập 20-25 triệu", "Thưởng KPI", "Bảo hiểm đầy đủ"],
    location: "<PERSON><PERSON>",
    workType: "onsite" as const,
    employmentType: "full-time" as const,
    experienceLevel: "senior" as const,
    salary: {
      min: 20000000,
      max: 25000000,
      currency: "VND",
      period: "monthly" as const,
    },
    company: {
      id: "c1",
      name: "CÔNG TY TNHH TRUYỀN THÔNG VÀ SÁNG TẠO HIMTEK",
      logo: "/company-logos/himtek.png",
      description: "Công ty chuyên về truyền thông và sáng tạo",
      website: "https://himtek.vn",
      size: "50-100",
      industry: "Marketing",
      location: "Hà Nội",
      rating: 4.2,
    },
    skills: [
      "TikTok Marketing",
      "E-commerce",
      "Team Management",
      "Digital Marketing",
    ],
    tags: ["Leader", "TikTok", "E-commerce"],
    postedAt: "2024-01-15T10:00:00Z",
    deadline: "2024-02-15T10:00:00Z",
    applicationsCount: 45,
    isBookmarked: false,
    featured: true,
    urgent: false,
    status: "active" as const,
  },
  {
    id: "2",
    title: "Nhân Viên Kinh Doanh B2B - Thu Nhập 15-20 Triệu",
    description: "Tuyển dụng nhân viên kinh doanh B2B với thu nhập hấp dẫn",
    summary: "Vị trí kinh doanh B2B với cơ hội phát triển và thu nhập ổn định",
    requirements: [
      "Kinh nghiệm 2+ năm kinh doanh B2B",
      "Kỹ năng giao tiếp tốt",
      "Tiếng Anh giao tiếp",
    ],
    benefits: [
      "Thu nhập 15-20 triệu",
      "Hoa hồng không giới hạn",
      "Du lịch công ty",
    ],
    location: "TP. Hồ Chí Minh",
    workType: "onsite" as const,
    employmentType: "full-time" as const,
    experienceLevel: "mid" as const,
    salary: {
      min: 15000000,
      max: 20000000,
      currency: "VND",
      period: "monthly" as const,
    },
    company: {
      id: "c2",
      name: "CÔNG TY CỔ PHẦN CÔNG NGHỆ ABC",
      logo: "/company-logos/abc-tech.png",
      description: "Công ty công nghệ hàng đầu",
      website: "https://abctech.vn",
      size: "100-200",
      industry: "Technology",
      location: "TP. Hồ Chí Minh",
      rating: 4.5,
    },
    skills: ["B2B Sales", "CRM", "Negotiation", "Customer Service"],
    tags: ["Sales", "B2B", "Technology"],
    postedAt: "2024-01-14T14:30:00Z",
    deadline: "2024-02-14T14:30:00Z",
    applicationsCount: 32,
    isBookmarked: true,
    featured: false,
    urgent: true,
    status: "active" as const,
  },
  {
    id: "3",
    title: "Frontend Developer React/Next.js - Lương Upto 25M",
    description:
      "Tuyển dụng Frontend Developer có kinh nghiệm với React và Next.js",
    summary:
      "Vị trí Frontend Developer với công nghệ hiện đại và mức lương cạnh tranh",
    requirements: [
      "3+ năm kinh nghiệm React/Next.js",
      "Thành thạo TypeScript",
      "Kinh nghiệm với Tailwind CSS",
    ],
    benefits: ["Lương upto 25M", "Làm việc remote", "Laptop MacBook Pro"],
    location: "Remote",
    workType: "remote" as const,
    employmentType: "full-time" as const,
    experienceLevel: "senior" as const,
    salary: {
      min: 18000000,
      max: 25000000,
      currency: "VND",
      period: "monthly" as const,
    },
    company: {
      id: "c3",
      name: "STARTUP TECH SOLUTIONS",
      logo: "/company-logos/startup-tech.png",
      description: "Startup công nghệ phát triển nhanh",
      website: "https://startuptech.vn",
      size: "20-50",
      industry: "Software Development",
      location: "Remote",
      rating: 4.8,
    },
    skills: ["React", "Next.js", "TypeScript", "Tailwind CSS", "Git"],
    tags: ["Frontend", "React", "Remote"],
    postedAt: "2024-01-13T09:15:00Z",
    deadline: "2024-02-13T09:15:00Z",
    applicationsCount: 67,
    isBookmarked: false,
    featured: true,
    urgent: true,
    status: "active" as const,
  },
  {
    id: "4",
    title: "Marketing Manager - Chiến Lược Thương Hiệu (Thu Nhập 18-22 Triệu)",
    description:
      "Tuyển dụng Marketing Manager có kinh nghiệm xây dựng chiến lược thương hiệu",
    summary:
      "Vị trí Marketing Manager với cơ hội phát triển thương hiệu và thu nhập hấp dẫn",
    requirements: [
      "Kinh nghiệm 4+ năm trong Marketing",
      "Hiểu biết về Digital Marketing",
      "Kỹ năng lãnh đạo team",
    ],
    benefits: ["Thu nhập 18-22 triệu", "Thưởng theo dự án", "Đào tạo nâng cao"],
    location: "Đà Nẵng",
    workType: "onsite" as const,
    employmentType: "full-time" as const,
    experienceLevel: "senior" as const,
    salary: {
      min: 18000000,
      max: 22000000,
      currency: "VND",
      period: "monthly" as const,
    },
    company: {
      id: "c4",
      name: "CÔNG TY TNHH MARKETING SOLUTIONS",
      logo: "/company-logos/marketing-sol.png",
      description: "Công ty chuyên về giải pháp marketing",
      website: "https://marketingsol.vn",
      size: "30-50",
      industry: "Marketing",
      location: "Đà Nẵng",
      rating: 4.3,
    },
    skills: ["Brand Strategy", "Digital Marketing", "Content Marketing", "SEO"],
    tags: ["Marketing", "Brand", "Strategy"],
    postedAt: "2024-01-12T11:20:00Z",
    deadline: "2024-02-12T11:20:00Z",
    applicationsCount: 28,
    isBookmarked: false,
    featured: true,
    urgent: false,
    status: "active" as const,
  },
  {
    id: "5",
    title: "Backend Developer Node.js/Python - Lương Upto 30M",
    description:
      "Tuyển dụng Backend Developer có kinh nghiệm với Node.js và Python",
    summary: "Vị trí Backend Developer với công nghệ hiện đại và mức lương cao",
    requirements: [
      "3+ năm kinh nghiệm Node.js/Python",
      "Kinh nghiệm với Database (MongoDB, PostgreSQL)",
      "Hiểu biết về Microservices",
    ],
    benefits: ["Lương upto 30M", "Flexible working time", "Cơ hội thăng tiến"],
    location: "Hà Nội",
    workType: "hybrid" as const,
    employmentType: "full-time" as const,
    experienceLevel: "senior" as const,
    salary: {
      min: 22000000,
      max: 30000000,
      currency: "VND",
      period: "monthly" as const,
    },
    company: {
      id: "c5",
      name: "FINTECH INNOVATION COMPANY",
      logo: "/company-logos/fintech-inn.png",
      description: "Công ty fintech đổi mới sáng tạo",
      website: "https://fintechinnovation.vn",
      size: "100-200",
      industry: "Fintech",
      location: "Hà Nội",
      rating: 4.7,
    },
    skills: ["Node.js", "Python", "MongoDB", "PostgreSQL", "Docker"],
    tags: ["Backend", "Node.js", "Python"],
    postedAt: "2024-01-11T16:45:00Z",
    deadline: "2024-02-11T16:45:00Z",
    applicationsCount: 89,
    isBookmarked: true,
    featured: true,
    urgent: true,
    status: "active" as const,
  },
  {
    id: "6",
    title: "UI/UX Designer - Thiết Kế Sản Phẩm (Thu Nhập 16-20 Triệu)",
    description:
      "Tuyển dụng UI/UX Designer có kinh nghiệm thiết kế sản phẩm digital",
    summary: "Vị trí UI/UX Designer với cơ hội sáng tạo và phát triển kỹ năng",
    requirements: [
      "2+ năm kinh nghiệm UI/UX Design",
      "Thành thạo Figma, Adobe XD",
      "Hiểu biết về User Research",
    ],
    benefits: ["Thu nhập 16-20 triệu", "Môi trường sáng tạo", "Thiết bị Apple"],
    location: "TP. Hồ Chí Minh",
    workType: "onsite" as const,
    employmentType: "full-time" as const,
    experienceLevel: "mid" as const,
    salary: {
      min: 16000000,
      max: 20000000,
      currency: "VND",
      period: "monthly" as const,
    },
    company: {
      id: "c6",
      name: "CREATIVE DESIGN STUDIO",
      logo: "/company-logos/creative-studio.png",
      description: "Studio thiết kế sáng tạo hàng đầu",
      website: "https://creativestudio.vn",
      size: "20-30",
      industry: "Design",
      location: "TP. Hồ Chí Minh",
      rating: 4.6,
    },
    skills: ["UI Design", "UX Design", "Figma", "Adobe XD", "Prototyping"],
    tags: ["Design", "UI/UX", "Creative"],
    postedAt: "2024-01-10T13:30:00Z",
    deadline: "2024-02-10T13:30:00Z",
    applicationsCount: 54,
    isBookmarked: false,
    featured: false,
    urgent: false,
    status: "active" as const,
  },
  {
    id: "7",
    title: "Data Analyst - Phân Tích Dữ Liệu (Thu Nhập 14-18 Triệu)",
    description:
      "Tuyển dụng Data Analyst có kinh nghiệm phân tích và xử lý dữ liệu",
    summary: "Vị trí Data Analyst với cơ hội làm việc với big data và AI",
    requirements: [
      "2+ năm kinh nghiệm Data Analysis",
      "Thành thạo SQL, Python, R",
      "Kinh nghiệm với Tableau, Power BI",
    ],
    benefits: ["Thu nhập 14-18 triệu", "Đào tạo AI/ML", "Môi trường tech"],
    location: "Hà Nội",
    workType: "hybrid" as const,
    employmentType: "full-time" as const,
    experienceLevel: "mid" as const,
    salary: {
      min: 14000000,
      max: 18000000,
      currency: "VND",
      period: "monthly" as const,
    },
    company: {
      id: "c7",
      name: "BIG DATA ANALYTICS CORP",
      logo: "/company-logos/bigdata-corp.png",
      description: "Công ty chuyên về phân tích dữ liệu lớn",
      website: "https://bigdataanalytics.vn",
      size: "50-100",
      industry: "Data Analytics",
      location: "Hà Nội",
      rating: 4.4,
    },
    skills: ["SQL", "Python", "R", "Tableau", "Power BI"],
    tags: ["Data", "Analytics", "Python"],
    postedAt: "2024-01-09T08:15:00Z",
    deadline: "2024-02-09T08:15:00Z",
    applicationsCount: 41,
    isBookmarked: false,
    featured: false,
    urgent: false,
    status: "active" as const,
  },
  {
    id: "8",
    title: "DevOps Engineer - Hệ Thống Cloud (Thu Nhập 20-28 Triệu)",
    description:
      "Tuyển dụng DevOps Engineer có kinh nghiệm với AWS, Docker, Kubernetes",
    summary: "Vị trí DevOps Engineer với công nghệ cloud hiện đại",
    requirements: [
      "3+ năm kinh nghiệm DevOps",
      "Thành thạo AWS, Docker, Kubernetes",
      "Kinh nghiệm CI/CD pipeline",
    ],
    benefits: ["Thu nhập 20-28 triệu", "AWS certification", "Remote flexible"],
    location: "Remote",
    workType: "remote" as const,
    employmentType: "full-time" as const,
    experienceLevel: "senior" as const,
    salary: {
      min: 20000000,
      max: 28000000,
      currency: "VND",
      period: "monthly" as const,
    },
    company: {
      id: "c8",
      name: "CLOUD INFRASTRUCTURE SOLUTIONS",
      logo: "/company-logos/cloud-infra.png",
      description: "Công ty chuyên về giải pháp hạ tầng cloud",
      website: "https://cloudinfra.vn",
      size: "100-200",
      industry: "Cloud Computing",
      location: "Remote",
      rating: 4.8,
    },
    skills: ["AWS", "Docker", "Kubernetes", "CI/CD", "Terraform"],
    tags: ["DevOps", "Cloud", "AWS"],
    postedAt: "2024-01-08T14:20:00Z",
    deadline: "2024-02-08T14:20:00Z",
    applicationsCount: 73,
    isBookmarked: true,
    featured: true,
    urgent: false,
    status: "active" as const,
  },
  {
    id: "9",
    title: "Product Manager - Quản Lý Sản Phẩm (Thu Nhập 25-35 Triệu)",
    description:
      "Tuyển dụng Product Manager có kinh nghiệm quản lý sản phẩm digital",
    summary:
      "Vị trí Product Manager với cơ hội dẫn dắt sản phẩm từ ý tưởng đến thị trường",
    requirements: [
      "4+ năm kinh nghiệm Product Management",
      "Hiểu biết về Agile, Scrum",
      "Kỹ năng phân tích thị trường",
    ],
    benefits: ["Thu nhập 25-35 triệu", "Stock options", "Leadership training"],
    location: "TP. Hồ Chí Minh",
    workType: "onsite" as const,
    employmentType: "full-time" as const,
    experienceLevel: "senior" as const,
    salary: {
      min: 25000000,
      max: 35000000,
      currency: "VND",
      period: "monthly" as const,
    },
    company: {
      id: "c9",
      name: "PRODUCT INNOVATION HUB",
      logo: "/company-logos/product-hub.png",
      description: "Hub đổi mới sáng tạo sản phẩm",
      website: "https://productinnovation.vn",
      size: "200-500",
      industry: "Product Development",
      location: "TP. Hồ Chí Minh",
      rating: 4.9,
    },
    skills: [
      "Product Strategy",
      "Agile",
      "Scrum",
      "Market Analysis",
      "Leadership",
    ],
    tags: ["Product", "Management", "Strategy"],
    postedAt: "2024-01-07T10:30:00Z",
    deadline: "2024-02-07T10:30:00Z",
    applicationsCount: 95,
    isBookmarked: false,
    featured: true,
    urgent: true,
    status: "active" as const,
  },
];

export default function JobsPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 py-6">
        {/* Header */}
        <div className="mb-6">
          <h1 className="text-xl font-semibold text-gray-900 mb-2">
            Việc làm tốt nhất
          </h1>
          <p className="text-gray-600 text-sm">
            Có <span className="font-medium text-blue-600">1,247</span> việc làm
            phù hợp với bạn
          </p>
        </div>

        {/* Job Grid - 3 columns */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {mockJobs.map((job) => (
            <JobCard
              key={job.id}
              job={job}
              variant="grid"
              onBookmark={(id) => console.log(`Bookmarked job: ${id}`)}
              onApply={(id) => console.log(`Applied to job: ${id}`)}
              onViewDetails={(id) =>
                console.log(`Viewing details for job: ${id}`)
              }
            />
          ))}
        </div>
      </div>
    </div>
  );
}
