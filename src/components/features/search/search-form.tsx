"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Search, MapPin } from "lucide-react";
import { useJobSearch, useJobFilters } from "@/hooks/use-jobs";
import { cn } from "@/lib/utils";

interface SearchFormProps {
  className?: string;
  onSearch?: (query: string, location: string) => void;
}

export function SearchForm({ className, onSearch }: SearchFormProps) {
  const [keywords, setKeywords] = useState("");
  const [location, setLocation] = useState("");
  const { handleSearch } = useJobSearch();
  const { updateFilters } = useJobFilters();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Update filters in store
    updateFilters({
      search: keywords,
      location: location,
    });

    // Handle search
    handleSearch(keywords);

    // Call external handler if provided
    onSearch?.(keywords, location);
  };

  return (
    <div
      className={cn(
        "bg-white rounded-lg border border-[#ecedf2] shadow-[0px_6px_15px_0px_rgba(64,79,104,0.05)] overflow-hidden",
        className
      )}
    >
      <form onSubmit={handleSubmit} className="flex h-[100px]">
        {/* Keywords Input */}
        <div className="flex-1 relative flex items-center px-6">
          <Search className="absolute left-6 h-5 w-5 text-[#696969] pointer-events-none" />
          <input
            type="text"
            value={keywords}
            onChange={(e) => setKeywords(e.target.value)}
            placeholder="Job title, keywords, or company"
            className="w-full pl-10 bg-transparent text-[15px] text-[#202124] placeholder:text-[#696969] outline-none font-normal"
          />
        </div>

        {/* Divider */}
        <div className="w-px bg-[#ecedf2] my-5" />

        {/* Location Input */}
        <div className="flex-1 relative flex items-center px-6">
          <MapPin className="absolute left-6 h-5 w-5 text-[#696969] pointer-events-none" />
          <input
            type="text"
            value={location}
            onChange={(e) => setLocation(e.target.value)}
            placeholder="City or postcode"
            className="w-full pl-10 bg-transparent text-[15px] text-[#202124] placeholder:text-[#696969] outline-none font-normal"
          />
        </div>

        {/* Find Jobs Button */}
        <div className="px-5 py-5">
          <Button
            type="submit"
            variant="primary"
            className="h-[60px] px-6 bg-[#1967d2] hover:bg-[#1557b0] text-white rounded-lg font-normal text-[15px]"
          >
            Find Jobs
          </Button>
        </div>
      </form>
    </div>
  );
}
