"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Search, MapPin, ChevronDown } from "lucide-react";
import { useJobSearch, useJobFilters } from "@/hooks/use-jobs";
import { cn } from "@/lib/utils";

interface AdvancedSearchFormProps {
  className?: string;
  onSearch?: (query: string, location: string, category: string) => void;
  showCategory?: boolean;
}

const categories = [
  { value: "", label: "All Categories" },
  { value: "technology", label: "Technology" },
  { value: "design", label: "Design" },
  { value: "marketing", label: "Marketing" },
  { value: "sales", label: "Sales" },
  { value: "finance", label: "Finance" },
  { value: "healthcare", label: "Healthcare" },
  { value: "education", label: "Education" },
];

export function AdvancedSearchForm({
  className,
  onSearch,
  showCategory = false,
}: AdvancedSearchFormProps) {
  const [keywords, setKeywords] = useState("");
  const [location, setLocation] = useState("");
  const [category, setCategory] = useState("");
  const { handleSearch } = useJobSearch();
  const { filters, updateFilters } = useJobFilters();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Update filters in store
    updateFilters({
      search: keywords,
      location: location,
      industry: category ? [category] : [],
    });

    // Handle search
    handleSearch(keywords);

    // Call external handler if provided
    onSearch?.(keywords, location, category);
  };

  return (
    <div
      className={cn(
        "bg-white rounded-lg border border-[#ecedf2] shadow-[0px_6px_15px_0px_rgba(64,79,104,0.05)] overflow-hidden",
        className
      )}
    >
      <form onSubmit={handleSubmit} className="flex h-[100px]">
        {/* Keywords Input */}
        <div className="flex-1 relative flex items-center px-6">
          <Search className="absolute left-6 h-5 w-5 text-[#696969] pointer-events-none" />
          <input
            type="text"
            value={keywords}
            onChange={(e) => setKeywords(e.target.value)}
            placeholder="Job title, keywords, or company"
            className="w-full pl-10 bg-transparent text-[15px] text-[#202124] placeholder:text-[#696969] outline-none font-normal"
          />
        </div>

        {/* Divider */}
        <div className="w-px bg-[#ecedf2] my-5" />

        {/* Location Input */}
        <div className="flex-1 relative flex items-center px-6">
          <MapPin className="absolute left-6 h-5 w-5 text-[#696969] pointer-events-none" />
          <input
            type="text"
            value={location}
            onChange={(e) => setLocation(e.target.value)}
            placeholder="City or postcode"
            className="w-full pl-10 bg-transparent text-[15px] text-[#202124] placeholder:text-[#696969] outline-none font-normal"
          />
        </div>

        {/* Category Select (Optional) */}
        {showCategory && (
          <>
            {/* Divider */}
            <div className="w-px bg-[#ecedf2] my-5" />

            <div className="flex-1 relative flex items-center px-6">
              <select
                value={category}
                onChange={(e) => setCategory(e.target.value)}
                className="w-full bg-transparent text-[15px] text-[#202124] outline-none font-normal appearance-none pr-8"
              >
                {categories.map((cat) => (
                  <option key={cat.value} value={cat.value}>
                    {cat.label}
                  </option>
                ))}
              </select>
              <ChevronDown className="absolute right-6 h-4 w-4 text-[#696969] pointer-events-none" />
            </div>
          </>
        )}

        {/* Find Jobs Button */}
        <div className="px-5 py-5">
          <Button
            type="submit"
            variant="primary"
            className="h-[60px] px-6 bg-[#1967d2] hover:bg-[#1557b0] text-white rounded-lg font-normal text-[15px]"
          >
            Find Jobs
          </Button>
        </div>
      </form>
    </div>
  );
}
