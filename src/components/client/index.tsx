"use client";

import { But<PERSON> as ButtonBase } from "@/components/ui/button";
import { Card as CardBase } from "@/components/ui/card";
import { Badge as BadgeBase } from "@/components/ui/badge";
import {
  Avatar as AvatarBase,
  AvatarImage as AvatarImageBase,
  AvatarFallback as AvatarFallbackBase,
} from "@/components/ui/avatar";
import { ComponentProps } from "react";

/**
 * Client Button component
 * Use this when you need to handle client-side interactivity with Button
 */
export function Button(props: ComponentProps<typeof ButtonBase>) {
  return <ButtonBase {...props} />;
}

/**
 * Client Card component
 * Use this for interactive cards on the client
 */
export function Card(props: ComponentProps<typeof CardBase>) {
  return <CardBase {...props} />;
}

/**
 * Client Badge component
 * Use this for interactive badges on the client
 */
export function Badge(props: ComponentProps<typeof BadgeBase>) {
  return <BadgeBase {...props} />;
}

/**
 * Client Avatar components
 * Use these for interactive avatars on the client
 */
export function Avatar(props: ComponentProps<typeof AvatarBase>) {
  return <AvatarBase {...props} />;
}

export function AvatarImage(props: ComponentProps<typeof AvatarImageBase>) {
  return <AvatarImageBase {...props} />;
}

export function AvatarFallback(
  props: ComponentProps<typeof AvatarFallbackBase>
) {
  return <AvatarFallbackBase {...props} />;
}
