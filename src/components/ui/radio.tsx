"use client";

import React from "react";
import { cn } from "@/lib/utils";

interface RadioProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  description?: string;
}

export const Radio = React.forwardRef<HTMLInputElement, RadioProps>(
  ({ className, label, description, ...props }, ref) => (
    <div className="flex items-start space-x-3">
      <input
        type="radio"
        ref={ref}
        className={cn(
          "h-4 w-4 shrink-0 rounded-full border border-[#696969] text-[#1967D2] focus:ring-2 focus:ring-[#1967D2] focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
          className
        )}
        {...props}
      />
      {(label || description) && (
        <div className="grid gap-1.5 leading-none">
          {label && (
            <label className="text-sm font-medium text-[#202124] leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
              {label}
            </label>
          )}
          {description && (
            <p className="text-xs text-[#696969]">{description}</p>
          )}
        </div>
      )}
    </div>
  )
);
Radio.displayName = "Radio";
