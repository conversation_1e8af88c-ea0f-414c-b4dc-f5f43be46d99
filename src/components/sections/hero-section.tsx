"use client";

import { SearchForm, MobileSearchForm } from "@/components/features/search";
import Image from "next/image";

export function HeroSection() {
  return (
    <section className="relative bg-gradient-to-br from-[#f0f5f7] to-[#e1f2e5] -mt-16 pt-32 pb-16 md:mt-0 md:py-20 lg:py-28 xl:py-32">
      <div className="container mx-auto px-4">
        <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-center">
          {/* Left Content */}
          <div className="space-y-6 md:space-y-8">
            <div className="space-y-3 md:space-y-4">
              <h1 className="text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-bold text-[#202124] leading-tight">
                There Are <span className="text-[#1967d2]">93,178</span>{" "}
                Postings Here
                <br />
                For You!
              </h1>
              <p className="text-sm md:text-base text-[#696969] max-w-lg">
                Find Jobs, Employment & Career Opportunities. Some of the
                companies we&apos;ve helped recruit excellent applicants over
                the years.
              </p>
            </div>

            {/* Search Form - Desktop */}
            <div className="hidden md:block space-y-4">
              <SearchForm className="max-w-4xl" />

              {/* Popular Searches - Desktop */}
              <div className="flex flex-wrap gap-2 items-center">
                <span className="text-sm text-[#696969]">Popular:</span>
                {[
                  "Designer",
                  "Developer",
                  "Web",
                  "IOS",
                  "PHP",
                  "Senior",
                  "Engineer",
                ].map((tag) => (
                  <button
                    key={tag}
                    className="text-sm text-[#1967d2] hover:text-[#1557b0] underline"
                  >
                    {tag}
                  </button>
                ))}
              </div>
            </div>

            {/* Mobile Search Form */}
            <div className="md:hidden space-y-4">
              <MobileSearchForm />
            </div>
          </div>

          {/* Right Illustration */}
          <div className="hidden lg:block">
            <div className="relative">
              {/* Main Illustration */}
              <div className="w-full h-80 xl:h-96 bg-gradient-to-br from-[#e1f2e5] to-[#f0f5f7] rounded-2xl flex items-center justify-center p-6 xl:p-8">
                <Image
                  src="/hero_right.png"
                  alt="Job Search Illustration"
                  width={400}
                  height={300}
                  className="w-full h-full object-contain"
                  priority
                />
              </div>

              {/* Floating Cards */}
              <div className="absolute -top-4 -right-4 bg-white rounded-lg p-3 xl:p-4 shadow-lg border border-white/20">
                <div className="text-xs xl:text-sm">
                  <div className="font-medium text-[#202124]">
                    10k+ Jobs Available
                  </div>
                  <div className="text-[#696969]">Updated daily</div>
                </div>
              </div>

              <div className="absolute -bottom-4 -left-4 bg-white rounded-lg p-3 xl:p-4 shadow-lg border border-white/20">
                <div className="text-xs xl:text-sm">
                  <div className="font-medium text-[#202124]">
                    500+ Companies
                  </div>
                  <div className="text-[#696969]">Are hiring</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
