import { Card, CardContent } from "@/components/ui/card";
import { Avatar } from "@/components/ui/avatar";
import { Star } from "lucide-react";

const testimonials = [
  {
    id: "1",
    name: "<PERSON>",
    role: "UI/UX Designer",
    company: "Corner Dot",
    avatar: "/avatars/robert-fox.jpg",
    rating: 5,
    content:
      "Ut ullamcorper hendrerit tempor. Aliquam in rutrum dui. Maecenas ac placerat metus, in faucibus est.",
  },
  {
    id: "2",
    name: "<PERSON><PERSON>",
    role: "Creative Director",
    company: "Photogram",
    avatar: "/avatars/bessie-cooper.jpg",
    rating: 5,
    content:
      "Mauris eget lorem odio. Mauris convallis justo molestie metus aliquam lacinia. Suspendisse ut dui vulputate augue condimentum ornare. Morbi vitae tristique ante",
  },
  {
    id: "3",
    name: "<PERSON>",
    role: "Photographer",
    company: "Capture Studios",
    avatar: "/avatars/jane-cooper.jpg",
    rating: 5,
    content:
      "Class aptent taciti sociosqu ad litora torquent per conubia nostra, per inceptos himenaeos. Suspendisse et magna quis.",
  },
];

export function TestimonialsSection() {
  return (
    <section className="py-16 lg:py-24 bg-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl lg:text-4xl font-bold text-[#202124] mb-4">
            Testimonials From Our Customers
          </h2>
          <p className="text-lg text-[#696969] max-w-2xl mx-auto">
            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Volutpat
            orci enim sed nisl elementum.
          </p>
        </div>

        {/* Testimonials Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {testimonials.map((testimonial) => (
            <Card
              key={testimonial.id}
              className="border-[#e0e6ed] hover:shadow-lg transition-shadow duration-300"
            >
              <CardContent className="p-6">
                {/* Rating Stars */}
                <div className="flex items-center mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star
                      key={i}
                      className="w-5 h-5 text-[#f9ab00] fill-current"
                    />
                  ))}
                </div>

                {/* Testimonial Content */}
                <blockquote className="text-[#696969] mb-6 leading-relaxed">
                  "{testimonial.content}"
                </blockquote>

                {/* Author Info */}
                <div className="flex items-center space-x-3">
                  <Avatar className="w-12 h-12 bg-[#1967d2]/10">
                    <div className="w-full h-full bg-gradient-to-br from-[#1967d2] to-[#1557b0] rounded-full flex items-center justify-center text-white font-medium">
                      {testimonial.name
                        .split(" ")
                        .map((n) => n[0])
                        .join("")}
                    </div>
                  </Avatar>
                  <div>
                    <div className="font-semibold text-[#202124]">
                      {testimonial.name}
                    </div>
                    <div className="text-sm text-[#696969]">
                      {testimonial.role} at {testimonial.company}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}
