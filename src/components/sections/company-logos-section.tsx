const companies = [
  {
    name: "Segment",
    logo: "/company-logos/segment.svg",
  },
  {
    name: "TechStart",
    logo: "/company-logos/techstart.svg",
  },
  {
    name: "DesignHub",
    logo: "/company-logos/designhub.svg",
  },
  {
    name: "Segment",
    logo: "/company-logos/segment.svg",
  },
  {
    name: "TechStart",
    logo: "/company-logos/techstart.svg",
  },
  {
    name: "DesignHub",
    logo: "/company-logos/designhub.svg",
  },
];

export function CompanyLogosSection() {
  return (
    <section className="py-16 bg-[#f0f5f7]">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-2xl lg:text-3xl font-bold text-[#202124] mb-2">
            Trusted By
          </h2>
          <p className="text-[#696969]">
            Leading companies already using our platform
          </p>
        </div>

        {/* Company Logos */}
        <div className="flex flex-wrap justify-center items-center gap-8 lg:gap-12">
          {companies.map((company, index) => (
            <div
              key={`${company.name}-${index}`}
              className="group cursor-pointer"
            >
              <img
                src={company.logo}
                alt={`${company.name} logo`}
                className="h-8 lg:h-10 w-auto opacity-60 hover:opacity-100 group-hover:scale-110 transition-all duration-300"
              />
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
