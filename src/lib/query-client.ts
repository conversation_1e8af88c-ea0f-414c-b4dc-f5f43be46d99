import { QueryClient } from "@tanstack/react-query";

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      gcTime: 1000 * 60 * 30, // 30 minutes (formerly cacheTime)
      refetchOnWindowFocus: false,
      retry: 1,
    },
    mutations: {
      retry: false,
    },
  },
});

// Query Keys Factory
export const queryKeys = {
  all: ["app"] as const,
  jobs: () => [...queryKeys.all, "jobs"] as const,
  job: (id: string) => [...queryKeys.jobs(), "job", id] as const,
  jobsSearch: (filters: any) =>
    [...queryKeys.jobs(), "search", filters] as const,
  companies: () => [...queryKeys.all, "companies"] as const,
  company: (id: string) => [...queryKeys.companies(), "company", id] as const,
  user: () => [...queryKeys.all, "user"] as const,
  userProfile: () => [...queryKeys.user(), "profile"] as const,
  applications: () => [...queryKeys.all, "applications"] as const,
  application: (id: string) =>
    [...queryKeys.applications(), "application", id] as const,
};
