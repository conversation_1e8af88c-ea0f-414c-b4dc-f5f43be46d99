export interface ApiResponse<T = any> {
  data: T;
  message: string;
  status: "success" | "error";
  timestamp: string;
}

export interface PaginatedResponse<T = any> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  message: string;
  status: "success" | "error";
  timestamp: string;
}

export interface ApiError {
  message: string;
  statusCode: number;
  error: string;
  timestamp: string;
}

export type QueryKey = readonly unknown[];

export interface LoadingState {
  isLoading: boolean;
  error: string | null;
}
