import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";
import { immer } from "zustand/middleware/immer";
import type { User, UserProfile } from "@/types/user";

interface UserStore {
  // State
  user: User | null;
  isAuthenticated: boolean;

  // Actions
  setUser: (user: User) => void;
  updateProfile: (profile: Partial<UserProfile>) => void;
  clearUser: () => void;

  // Computed
  get userDisplayName(): string;
}

export const useUserStore = create<UserStore>()(
  devtools(
    persist(
      immer((set, get) => ({
        // Initial state
        user: null,
        isAuthenticated: false,

        // Actions
        setUser: (user) =>
          set((state) => {
            state.user = user;
            state.isAuthenticated = true;
          }),

        updateProfile: (profileUpdates) =>
          set((state) => {
            if (state.user) {
              state.user.profile = { ...state.user.profile, ...profileUpdates };
            }
          }),

        clearUser: () =>
          set((state) => {
            state.user = null;
            state.isAuthenticated = false;
          }),

        // Computed properties
        get userDisplayName() {
          const user = get().user;
          if (!user) return "";

          const { firstName, lastName } = user.profile;
          if (firstName && lastName) {
            return `${firstName} ${lastName}`;
          }

          return user.name || user.email;
        },
      })),
      {
        name: "user-store",
        partialize: (state) => ({
          user: state.user,
          isAuthenticated: state.isAuthenticated,
        }),
      }
    ),
    {
      name: "user-store",
    }
  )
);
