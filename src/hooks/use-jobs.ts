import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import type { Job, JobFilters } from "@/types/job";
import type { PaginatedResponse } from "@/types/api";
import { queryKeys } from "@/lib/query-client";
import { useJobStore } from "@/stores/job-store";

// Mock API functions - replace with actual API calls
const fetchJobs = async (
  filters: JobFilters = {},
  page = 1,
  limit = 20
): Promise<PaginatedResponse<Job>> => {
  // This would be replaced with actual API call
  const response = await fetch(
    `/api/jobs?${new URLSearchParams({
      ...filters,
      page: page.toString(),
      limit: limit.toString(),
    } as any)}`
  );
  return response.json();
};

const fetchJob = async (id: string): Promise<Job> => {
  const response = await fetch(`/api/jobs/${id}`);
  return response.json();
};

const applyToJob = async (data: {
  jobId: string;
  coverLetter: string;
  resumeFile: File;
}) => {
  const formData = new FormData();
  formData.append("jobId", data.jobId);
  formData.append("coverLetter", data.coverLetter);
  formData.append("resume", data.resumeFile);

  const response = await fetch("/api/applications", {
    method: "POST",
    body: formData,
  });
  return response.json();
};

const bookmarkJob = async (jobId: string) => {
  const response = await fetch(`/api/jobs/${jobId}/bookmark`, {
    method: "POST",
  });
  return response.json();
};

// Custom hooks
export function useJobsQuery(page = 1, limit = 20) {
  const filters = useJobStore((state) => state.filters);

  return useQuery({
    queryKey: queryKeys.jobsSearch({ filters, page, limit }),
    queryFn: () => fetchJobs(filters, page, limit),
    staleTime: 1000 * 60 * 5, // 5 minutes
    placeholderData: (previousData) => previousData,
  });
}

export function useJobQuery(id: string) {
  return useQuery({
    queryKey: queryKeys.job(id),
    queryFn: () => fetchJob(id),
    enabled: !!id,
  });
}

export function useJobApplication() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: applyToJob,
    onSuccess: () => {
      toast.success("Application submitted successfully!");
      // Invalidate applications queries
      queryClient.invalidateQueries({ queryKey: queryKeys.applications() });
    },
    onError: (error: any) => {
      toast.error(error.message || "Failed to submit application");
    },
  });
}

export function useJobBookmark() {
  const queryClient = useQueryClient();
  const toggleSaveJob = useJobStore((state) => state.toggleSaveJob);

  return useMutation({
    mutationFn: bookmarkJob,
    onSuccess: (data, variables) => {
      toggleSaveJob(variables);
      toast.success("Job bookmark updated!");
      // Update job queries cache
      queryClient.invalidateQueries({ queryKey: queryKeys.jobs() });
    },
    onError: (error: any) => {
      toast.error(error.message || "Failed to update bookmark");
    },
  });
}

export function useJobSearch() {
  const updateFilters = useJobStore((state) => state.updateFilters);
  const addRecentSearch = useJobStore((state) => state.addRecentSearch);

  const handleSearch = (query: string) => {
    updateFilters({ search: query });
    if (query.trim()) {
      addRecentSearch(query.trim());
    }
  };

  return { handleSearch };
}

export function useJobFilters() {
  const filters = useJobStore((state) => state.filters);
  const updateFilters = useJobStore((state) => state.updateFilters);
  const clearFilters = useJobStore((state) => state.clearFilters);
  const hasActiveFiltersFunc = useJobStore((state) => state.hasActiveFilters);

  // Call the function to get the boolean value
  const hasActiveFilters = hasActiveFiltersFunc();

  return {
    filters,
    updateFilters,
    clearFilters,
    hasActiveFilters,
  };
}
