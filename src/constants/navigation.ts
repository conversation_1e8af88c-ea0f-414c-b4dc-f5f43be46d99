import { Briefcase, Building2, Users, BookOpen } from "lucide-react";

export interface NavigationChild {
  label: string;
  href: string;
  description?: string;
}

export interface NavigationItem {
  id: string;
  label: string;
  href: string;
  icon?: any;
  children?: NavigationChild[];
}

export const NAVIGATION_ITEMS: NavigationItem[] = [
  {
    id: "find-jobs",
    label: "Find Jobs",
    href: "/jobs",
    icon: Briefcase,
    children: [
      {
        label: "Browse Jobs",
        href: "/jobs",
        description: "Search through all available positions",
      },
      {
        label: "Browse Categories",
        href: "/categories",
        description: "Explore jobs by industry and type",
      },
      {
        label: "Job Alerts",
        href: "/job-alerts",
        description: "Get notified about new opportunities",
      },
      {
        label: "My Bookmarks",
        href: "/bookmarks",
        description: "View your saved job listings",
      },
    ],
  },
  {
    id: "employers",
    label: "Employers",
    href: "/employers",
    icon: Building2,
    children: [
      {
        label: "Browse Companies",
        href: "/companies",
        description: "Discover amazing workplaces",
      },
      {
        label: "Employer Dashboard",
        href: "/employer/dashboard",
        description: "Manage your company profile",
      },
      {
        label: "Add Job",
        href: "/employer/add-job",
        description: "Post new job opportunities",
      },
      {
        label: "Job Packages",
        href: "/employer/packages",
        description: "Choose the right posting plan",
      },
    ],
  },
  {
    id: "candidates",
    label: "Candidates",
    href: "/candidates",
    icon: Users,
    children: [
      {
        label: "Browse Candidates",
        href: "/candidates",
        description: "Search through candidate profiles",
      },
      {
        label: "Candidate Dashboard",
        href: "/candidate/dashboard",
        description: "Manage your professional profile",
      },
    ],
  },
  {
    id: "blog",
    label: "Blog",
    href: "/blog",
    icon: BookOpen,
  },
] as const;

export const COMPANY_INFO = {
  name: "Superio",
  shortName: "S",
  logo: "/logo.svg",
  description: "Find your dream job",
  tagline: "Your career journey starts here",
} as const;

export const SOCIAL_LINKS = [
  { name: "Twitter", href: "https://twitter.com/superio", icon: "Twitter" },
  {
    name: "LinkedIn",
    href: "https://linkedin.com/company/superio",
    icon: "Linkedin",
  },
  { name: "Facebook", href: "https://facebook.com/superio", icon: "Facebook" },
  {
    name: "Instagram",
    href: "https://instagram.com/superio",
    icon: "Instagram",
  },
] as const;
